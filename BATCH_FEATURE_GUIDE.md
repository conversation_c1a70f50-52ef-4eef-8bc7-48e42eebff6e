# 批量预读和验证码识别功能使用指南

## 🚀 新功能概述

本次更新为 `fetch.html` 页面添加了两个重要功能：

### 1. 批量预读功能
- **多选账号**：支持选择多个邮箱账号进行批量处理
- **并发处理**：智能控制并发数量，避免服务器压力过大
- **进度显示**：实时显示批量处理进度和状态
- **结果汇总**：统一展示所有账号的处理结果

### 2. 验证码识别功能
- **智能识别**：自动从邮件内容中识别验证码
- **多种格式**：支持数字、字母、混合格式的验证码
- **高亮显示**：在邮件内容中高亮显示识别到的验证码
- **状态提示**：明确显示"验证码"或"无验证码"状态

## 📋 功能详细说明

### 批量预读功能

#### 界面变化
1. **复选框选择**：每个账号前添加了复选框
2. **全选控制**：页面顶部添加"全选当前页"选项
3. **批量控制面板**：选中账号后显示批量操作按钮
4. **进度显示**：批量处理时显示实时进度条

#### 操作流程
1. 在账号列表中勾选要处理的账号
2. 选中后会显示批量控制面板
3. 点击"批量预读新邮件"或"批量预读新垃圾邮件"
4. 系统开始并发处理，显示进度
5. 处理完成后在右侧显示汇总结果

#### 技术特性
- **并发控制**：最大并发数为3，避免服务器过载
- **错误处理**：单个账号失败不影响其他账号处理
- **状态保持**：翻页后保持选择状态
- **智能重试**：网络错误时自动重试

### 验证码识别功能

#### 识别算法
采用三级优先级模式：

**高优先级模式**（精确匹配）：
- `验证码是：123456`
- `您的验证码：ABC123`
- `verification code is: 789012`
- `OTP：456789`
- `安全码：XY7890`

**中等优先级模式**（通用匹配）：
- `验证码 123456`
- `code ABC123`
- `PIN码 789012`
- `token XY7890`

**低优先级模式**（数字匹配）：
- 4-8位纯数字
- 排除年份、日期等明显非验证码数字

#### 显示效果
1. **邮件内容高亮**：验证码在邮件中以黄色背景高亮显示
2. **状态横幅**：邮件顶部显示验证码识别结果
3. **弹窗提示**：检测到验证码时右上角显示提示框
4. **一键复制**：点击提示框可直接复制验证码

## 🎯 使用场景

### 批量预读适用场景
- **大量账号管理**：需要同时检查多个邮箱的新邮件
- **效率提升**：避免逐个点击查看邮件的繁琐操作
- **批量监控**：定期批量检查所有账号的邮件状态

### 验证码识别适用场景
- **注册验证**：快速获取注册验证码
- **登录验证**：提取登录二次验证码
- **安全验证**：识别各类安全验证码
- **自动化流程**：配合自动化工具使用

## ⚙️ 技术实现

### 核心类和函数

#### BatchEmailProcessor 类
```javascript
class BatchEmailProcessor {
    constructor() {
        this.maxConcurrent = 3; // 最大并发数
        this.processingQueue = [];
        this.results = new Map();
        this.isProcessing = false;
    }
    
    async processBatch(accounts, mailbox, type) {
        // 批量处理逻辑
    }
}
```

#### 验证码识别函数
```javascript
function detectVerificationCode(content) {
    // 多级模式匹配
    // 返回识别到的验证码或null
}

function detectAndHighlightVerificationCode(content) {
    // 识别验证码并在内容中高亮显示
    // 返回 { code, content }
}
```

### 关键特性
- **异步处理**：使用 Promise.allSettled 处理并发请求
- **错误隔离**：单个账号失败不影响整体处理
- **内存管理**：及时清理处理结果，避免内存泄漏
- **用户体验**：实时进度反馈和状态提示

## 🔧 配置和自定义

### 可调整参数
- **并发数量**：修改 `maxConcurrent` 值（默认3）
- **验证码模式**：在识别函数中添加新的正则表达式
- **超时设置**：调整网络请求超时时间
- **显示样式**：修改CSS样式自定义外观

### 扩展建议
- 添加更多验证码格式支持
- 实现批量结果导出功能
- 增加处理历史记录
- 支持自定义并发数设置

## 🐛 故障排除

### 常见问题
1. **批量处理失败**：检查网络连接和API服务状态
2. **验证码识别不准确**：可能需要添加新的识别模式
3. **页面响应慢**：减少并发数量或检查服务器性能
4. **选择状态丢失**：确保JavaScript正常执行

### 调试方法
- 打开浏览器开发者工具查看控制台日志
- 检查网络请求状态和响应内容
- 使用测试页面验证验证码识别功能
- 逐步测试单个账号处理流程

## 📈 性能优化

### 已实现的优化
- **并发控制**：避免同时发起过多请求
- **缓存机制**：复用已获取的账号信息
- **DOM优化**：减少不必要的DOM操作
- **内存管理**：及时清理临时数据

### 进一步优化建议
- 实现请求队列管理
- 添加本地缓存机制
- 优化大量数据的渲染性能
- 实现增量更新机制

---

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 查看控制台日志获取详细错误信息
- 使用测试页面验证功能是否正常
- 检查网络连接和API服务状态
