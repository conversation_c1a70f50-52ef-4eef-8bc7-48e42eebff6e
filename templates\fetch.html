<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>取件操作 - 邮箱 API 客户端</title>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ LOGO_URL }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ LOGO_URL }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ LOGO_URL }}">
    <style>
        :root {
            --bg-color: #f8f9fa;
            --text-color: #212529;
            --accent-color: #0078d4; /* Outlook blue */
            --card-bg: #fff;
            --card-border: #c8c8c8;
            --button-primary: #0078d4; /* Outlook blue */
            --button-primary-hover: #005a9e;
            --button-secondary: #f3f3f3;
            --button-secondary-hover: #e0e0e0;
            --header-bg: #fff;
            --header-border-bottom: #e0e0e0;
            --form-label-color: #495057;
            --success-color: #28a745;
            --error-color: #dc3545;
            --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #121212;
                --text-color: #f0f0f0;
                --accent-color: #3ab7f0;
                --card-bg: #1e1e1e;
                --card-border: #444;
                --button-primary: #3ab7f0;
                --button-primary-hover: #2a88b8;
                --header-bg: #1e1e1e;
                --header-border-bottom: #444;
                --form-label-color: #bbb;
                --success-color: #4caf50;
                --error-color: #f44336;
                --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            }
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            width: 95%;
            max-width: 1400px;
            padding-bottom: 50px;
            margin-top: 20px;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close-btn {
            background-color: #dc3545; /* 红色背景 */
            color: white;
            border: none;
            border-radius: 4px; /* 方形边角 */
            padding: 8px 16px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
        }
        
        .close-btn:hover {
            background-color: #c82333; /* 深红色悬停效果 */
            transform: translateY(-2px);
        }

        /* 双面板布局 */
        .split-layout {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .left-panel {
            flex: 0 0 350px;
            min-width: 320px;
        }

        .right-panel {
            flex: 1;
            min-height: 600px;
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 20px;
            box-shadow: var(--card-shadow);
        }

        /* 邮件显示区样式 */
        .mail-viewer {
            width: 100%;
            height: 100%;
            min-height: 600px;
            border: none;
            background-color: var(--card-bg);
        }

        .mail-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            min-height: 600px;
            color: #888;
        }

        .mail-placeholder i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: var(--accent-color);
        }

        h1 {
            color: var(--accent-color);
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            letter-spacing: -0.5px;
        }

        h2 {
            font-size: 1.5rem;
            color: var(--text-color);
            margin-top: 0;
            margin-bottom: 1rem;
            font-weight: 600;
            letter-spacing: -0.3px;
        }

        .box {
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: var(--card-shadow);
            transition: all 0.2s ease;
            animation: fadeIn 0.3s ease-out forwards;
        }

        button {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.95rem;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        button:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background-color: var(--button-primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--button-primary-hover);
        }

        .btn-secondary {
            background-color: var(--button-secondary);
            color: var(--text-color);
            border: 1px solid var(--card-border);
        }

        .btn-secondary:hover {
            background-color: var(--button-secondary-hover);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 账号列表样式 */
        .accounts-list {
            margin-top: 20px;
        }
        
        .account-item {
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: var(--card-shadow);
            transition: all 0.2s ease;
            animation: fadeIn 0.3s ease-out forwards;
        }
        
        .account-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }
        
        .account-email {
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 12px;
            color: var(--accent-color);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .mail-count {
            background-color: var(--accent-color);
            color: white;
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 0.8rem;
            min-width: 24px;
            text-align: center;
        }
        
        .account-email:hover {
            text-decoration: underline;
        }
        
        .account-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .btn-mail {
            flex: 1;
            min-width: 120px;
            font-size: 0.85rem;
            padding: 8px 12px;
            background-color: var(--button-secondary);
            color: var(--text-color);
            border: 1px solid var(--card-border);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-mail i {
            margin-right: 6px;
        }
        
        .btn-mail:hover {
            background-color: var(--button-secondary-hover);
        }
        
        .loading-spinner {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 0;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(0, 120, 212, 0.2);
            border-top-color: var(--accent-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 12px;
        }
        
        #pageInfo {
            min-width: 100px;
            text-align: center;
        }
        
        .no-accounts, .error-message {
            text-align: center;
            padding: 30px;
            color: #666;
        }
        
        .error-message {
            color: var(--error-color);
        }
        
        .back-button {
            display: inline-flex;
            align-items: center;
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-button i {
            margin-right: 6px;
        }
        
        .back-button:hover {
            text-decoration: underline;
        }
        
        .notification {
            position: fixed;
            bottom: -60px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 4px;
            z-index: 1000;
            transition: bottom 0.3s ease-out;
        }
        
        .notification.success {
            background-color: rgba(40, 167, 69, 0.9);
        }
        
        .notification.error {
            background-color: rgba(220, 53, 69, 0.9);
        }
        
        .notification.show {
            bottom: 20px;
            opacity: 1;
            transform: translateY(0);
        }

        /* 加载中遮罩 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 10;
            border-radius: 8px;
        }

        .active-account {
            background-color: rgba(0, 120, 212, 0.1);
            border-left: 3px solid var(--accent-color);
        }

        /* 批量操作控制面板样式 */
        .batch-control-panel {
            background-color: rgba(0, 120, 212, 0.1);
            border: 1px solid var(--accent-color);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            animation: fadeIn 0.3s ease-out forwards;
        }

        .batch-info {
            font-weight: 500;
            color: var(--accent-color);
            margin-bottom: 12px;
            text-align: center;
        }

        .batch-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .batch-btn {
            flex: 1;
            min-width: 120px;
            font-size: 0.85rem;
            padding: 8px 12px;
        }

        /* 全选控制样式 */
        .select-all-control {
            margin-bottom: 16px;
            padding: 12px;
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 6px;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: 500;
            color: var(--text-color);
        }

        .checkbox-container input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
            cursor: pointer;
        }

        /* 账号项复选框样式 */
        .account-checkbox {
            margin-right: 12px;
            transform: scale(1.1);
            cursor: pointer;
        }

        .account-item.selected {
            background-color: rgba(0, 120, 212, 0.1);
            border-left: 3px solid var(--accent-color);
        }

        .account-email-container {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        /* 批量处理进度显示 */
        .batch-progress {
            margin-top: 16px;
            padding: 16px;
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            display: none;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 12px;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--accent-color);
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-details {
            font-size: 0.9rem;
            color: #666;
        }

        /* 响应式调整 */
        @media (max-width: 1024px) {
            .split-layout {
                flex-direction: column;
            }

            .left-panel {
                flex: 1;
                width: 100%;
            }

            .right-panel {
                flex: 1;
                width: 100%;
            }

            .batch-actions {
                flex-direction: column;
            }

            .batch-btn {
                width: 100%;
            }
        }

        .email-list {
            margin-bottom: 20px;
        }
        .email-item {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #fff;
        }
        .email-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        .email-header h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #333;
        }
        .email-header p {
            margin: 5px 0;
            color: #555;
        }
        .email-content {
            padding: 10px 0;
        }

        /* 批量结果显示样式 */
        .batch-results {
            padding: 20px;
        }

        .batch-results h3 {
            color: var(--accent-color);
            margin-bottom: 20px;
            text-align: center;
        }

        .result-item {
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background-color: var(--card-bg);
        }

        .result-item.success {
            border-left: 4px solid var(--success-color);
        }

        .result-item.error {
            border-left: 4px solid var(--error-color);
        }

        .result-item h4 {
            margin: 0 0 12px 0;
            color: var(--text-color);
            font-size: 1.1rem;
        }

        .verification-code {
            margin: 8px 0;
            padding: 8px 12px;
            background-color: rgba(0, 120, 212, 0.1);
            border-radius: 4px;
            font-weight: 500;
        }

        .code-value {
            color: var(--accent-color);
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
            font-weight: bold;
            background-color: rgba(0, 120, 212, 0.2);
            padding: 2px 6px;
            border-radius: 3px;
        }

        /* 确保批量结果中的验证码也有点击样式 */
        .batch-results .highlighted-code {
            font-size: 1.1rem;
            margin-left: 8px;
        }

        .timestamp {
            font-size: 0.9rem;
            color: #666;
            margin: 8px 0;
        }

        .email-preview {
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            font-size: 0.9rem;
            color: #666;
            max-height: 100px;
            overflow: hidden;
            margin-top: 12px;
        }

        .error-msg {
            color: var(--error-color);
            font-weight: 500;
            margin: 8px 0;
        }

        /* 验证码高亮样式 */
        .highlighted-code {
            background-color: #ffeb3b;
            color: #333;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            border: 2px solid #ffc107;
            animation: pulse 2s infinite;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            display: inline-block;
        }

        .highlighted-code:hover {
            background-color: #ffc107;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.4);
        }

        .highlighted-code:active {
            transform: scale(0.95);
            background-color: #ff9800;
        }

        .highlighted-code::after {
            content: "点击复制";
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: normal;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease;
            z-index: 1000;
        }

        .highlighted-code:hover::after {
            opacity: 1;
        }

        .highlighted-code.copied {
            background-color: #4caf50 !important;
            border-color: #388e3c !important;
            animation: copySuccess 0.6s ease;
        }

        @keyframes copySuccess {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
        }

        /* 验证码提示框 */
        .verification-code-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: var(--success-color);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1001;
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .verification-code-alert .code-display {
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
            font-weight: bold;
            margin-left: 8px;
        }

        /* 验证码状态显示 */
        .verification-code-status {
            margin: 8px 0;
            padding: 8px 12px;
            background-color: rgba(0, 120, 212, 0.1);
            border-radius: 4px;
            border-left: 3px solid var(--accent-color);
        }

        .verification-code-banner {
            background-color: var(--accent-color);
            color: white;
            padding: 12px 16px;
            margin-bottom: 16px;
            border-radius: 6px;
            text-align: center;
            font-weight: 500;
        }

        .verification-code-banner .code-value {
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            font-weight: bold;
            background-color: rgba(255, 255, 255, 0.2);
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header-top">
            <h1>邮箱 API 客户端</h1>
            <button id="closeAppBtn" class="close-btn">
                <i class="ri-close-circle-line"></i> 关闭程序
            </button>
        </div>
        
        <a href="/" class="back-button">
            <i class="ri-arrow-left-line"></i> 返回首页
        </a>

        <h2><i class="ri-mail-line"></i> 取件操作</h2>
        <p>选择账号和操作类型：</p>
        
        <div class="split-layout">
            <!-- 左侧面板：账号列表 -->
            <div class="left-panel box">
                <!-- 批量操作控制面板 -->
                <div class="batch-control-panel" id="batchControlPanel" style="display:none;">
                    <div class="batch-info">
                        <span id="selectedCount">0</span> 个账号已选中
                    </div>
                    <div class="batch-actions">
                        <button id="batchNewMailBtn" class="btn-primary batch-btn">
                            <i class="ri-mail-line"></i> 批量预读新邮件
                        </button>
                        <button id="batchNewJunkBtn" class="btn-primary batch-btn">
                            <i class="ri-spam-2-line"></i> 批量预读新垃圾邮件
                        </button>
                        <button id="clearSelectionBtn" class="btn-secondary batch-btn">
                            <i class="ri-close-line"></i> 清除选择
                        </button>
                    </div>
                </div>

                <!-- 全选控制 -->
                <div class="select-all-control">
                    <label class="checkbox-container">
                        <input type="checkbox" id="selectAllCheckbox">
                        <span class="checkmark"></span>
                        全选当前页
                    </label>
                </div>

                <div id="accounts" class="accounts-list">
                    <!-- 账号列表将通过JavaScript动态加载 -->
                    <div class="loading-spinner" id="loadingSpinner">
                        <div class="spinner"></div>
                        <p>加载中...</p>
                    </div>
                </div>

                <div class="pagination">
                    <button id="prevPageBtn" class="btn-secondary" disabled>上一页</button>
                    <span id="pageInfo">第 1 页</span>
                    <button id="nextPageBtn" class="btn-secondary" disabled>下一页</button>
                </div>
            </div>
            
            <!-- 右侧面板：邮件内容 -->
            <div class="right-panel" id="mailContainer">
                <div class="mail-placeholder" id="mailPlaceholder">
                    <i class="ri-mail-open-line"></i>
                    <p>选择一个账号并点击相应的邮件类型按钮以查看邮件</p>
                </div>
                <div id="mailViewer" style="display:none;height:100%;"></div>
                <div id="mailLoadingOverlay" class="loading-overlay" style="display:none;">
                    <div class="spinner"></div>
                    <p>加载邮件中...</p>
                </div>
            </div>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始加载账号
            loadAccounts(1);

            // 设置验证码点击事件委托
            setupVerificationCodeClickHandlers();

            // 直接绑定按钮事件，而不是使用onclick属性
            document.getElementById('prevPageBtn').addEventListener('click', function() {
                changePage(-1);
            });

            document.getElementById('nextPageBtn').addEventListener('click', function() {
                changePage(1);
            });
            
            // 关闭程序按钮功能
            document.getElementById('closeAppBtn').addEventListener('click', function() {
                if (confirm('确定要关闭程序吗？')) {
                    fetch('/shutdown', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'shutting_down') {
                            alert('程序正在关闭，关闭完成请刷新页面');
                        }
                    })
                    .catch(error => {
                        console.error('关闭程序失败:', error);
                        alert('关闭程序失败，请尝试手动关闭窗口');
                    });
                }
            });

            // 批量操作按钮事件监听
            document.getElementById('selectAllCheckbox').addEventListener('change', function() {
                handleSelectAll(this.checked);
            });

            document.getElementById('batchNewMailBtn').addEventListener('click', function() {
                processBatchEmails('INBOX', 'new');
            });

            document.getElementById('batchNewJunkBtn').addEventListener('click', function() {
                processBatchEmails('Junk', 'new');
            });

            document.getElementById('clearSelectionBtn').addEventListener('click', function() {
                clearSelection();
            });
        });
        
        const API_BASE_URL = '{{ API_BASE_URL }}';
        const ACCOUNTS_PER_PAGE = 10;
        let currentPage = 1;
        let totalAccounts = {};
        let totalPages = 1;
        let currentActiveAccount = null;

        // 批量处理相关变量
        let selectedAccounts = new Set();
        let batchProcessor = null;

        // 批量邮件处理管理器
        class BatchEmailProcessor {
            constructor() {
                this.maxConcurrent = 3; // 最大并发数
                this.processingQueue = [];
                this.results = new Map();
                this.isProcessing = false;
            }

            async processBatch(accounts, mailbox, type) {
                if (this.isProcessing) {
                    showNotification('批量处理正在进行中，请稍候', 'error');
                    return;
                }

                this.isProcessing = true;
                this.results.clear();

                try {
                    // 显示进度面板
                    this.showProgressPanel(accounts.length);

                    // 分批处理
                    const batches = this.createBatches(accounts, this.maxConcurrent);
                    let processedCount = 0;

                    for (const batch of batches) {
                        const promises = batch.map(email => this.processAccount(email, mailbox, type));
                        const batchResults = await Promise.allSettled(promises);

                        // 更新进度
                        processedCount += batch.length;
                        this.updateProgress(processedCount, accounts.length);

                        // 处理结果
                        batchResults.forEach((result, index) => {
                            const email = batch[index];
                            if (result.status === 'fulfilled') {
                                this.results.set(email, result.value);
                            } else {
                                this.results.set(email, { error: result.reason.message || '处理失败' });
                            }
                        });
                    }

                    // 显示批量结果
                    this.displayBatchResults();
                    showNotification(`批量处理完成，共处理 ${accounts.length} 个账号`, 'success');

                } catch (error) {
                    console.error('批量处理失败:', error);
                    showNotification('批量处理失败: ' + error.message, 'error');
                } finally {
                    this.isProcessing = false;
                    this.hideProgressPanel();
                }
            }

            createBatches(accounts, batchSize) {
                const batches = [];
                for (let i = 0; i < accounts.length; i += batchSize) {
                    batches.push(accounts.slice(i, i + batchSize));
                }
                return batches;
            }

            async processAccount(email, mailbox, type) {
                // 先增加计数
                await fetch(`/api/mail/count/${email}`, { method: 'POST' });

                // 构建API URL
                const url = buildApiUrl(email, mailbox, type);
                if (!url) {
                    throw new Error('无法构建API URL');
                }

                // 获取邮件内容
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status}`);
                }

                const content = await response.text();

                // 识别验证码
                const verificationCode = detectVerificationCode(content);

                return {
                    email,
                    content,
                    verificationCode,
                    timestamp: new Date().toLocaleString()
                };
            }

            showProgressPanel(total) {
                const progressPanel = document.createElement('div');
                progressPanel.id = 'batchProgressPanel';
                progressPanel.className = 'batch-progress';
                progressPanel.style.display = 'block';
                progressPanel.innerHTML = `
                    <div class="progress-header">
                        <span>批量处理进度</span>
                        <span id="progressText">0 / ${total}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-details" id="progressDetails">准备开始处理...</div>
                `;

                const leftPanel = document.querySelector('.left-panel');
                leftPanel.appendChild(progressPanel);
            }

            updateProgress(processed, total) {
                const progressText = document.getElementById('progressText');
                const progressFill = document.getElementById('progressFill');
                const progressDetails = document.getElementById('progressDetails');

                if (progressText) progressText.textContent = `${processed} / ${total}`;
                if (progressFill) progressFill.style.width = `${(processed / total) * 100}%`;
                if (progressDetails) progressDetails.textContent = `已处理 ${processed} 个账号，剩余 ${total - processed} 个`;
            }

            hideProgressPanel() {
                const progressPanel = document.getElementById('batchProgressPanel');
                if (progressPanel) {
                    progressPanel.remove();
                }
            }

            displayBatchResults() {
                let resultsHtml = '<div class="batch-results"><h3>批量处理结果</h3>';

                this.results.forEach((result, email) => {
                    if (result.error) {
                        resultsHtml += `
                            <div class="result-item error">
                                <h4>${email}</h4>
                                <p class="error-msg">错误: ${result.error}</p>
                            </div>
                        `;
                    } else {
                        // 为批量结果中的验证码生成唯一ID
                        const codeElementId = 'batch-code-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                        const codeDisplay = result.verificationCode
                            ? `<span class="highlighted-code" id="${codeElementId}" data-code="${result.verificationCode}" onclick="copyVerificationCode('${result.verificationCode}', '${codeElementId}')" title="点击复制验证码">${result.verificationCode}</span>`
                            : '无验证码';

                        resultsHtml += `
                            <div class="result-item success">
                                <h4>${email}</h4>
                                <p class="verification-code">
                                    <strong>验证码:</strong>
                                    ${codeDisplay}
                                </p>
                                <p class="timestamp">处理时间: ${result.timestamp}</p>
                                <div class="email-preview">
                                    ${result.content.substring(0, 200)}...
                                </div>
                            </div>
                        `;
                    }
                });

                resultsHtml += '</div>';

                // 显示结果
                showMailContent(resultsHtml);
            }
        }

        // 验证码识别函数
        function detectVerificationCode(content) {
            // 移除HTML标签，获取纯文本
            let textContent = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ');

            // 超高优先级模式 - 针对特定格式的精确匹配
            const ultraHighPriorityPatterns = [
                // 专门针对 "您的验证码为: e9b1e1" 这种格式
                /您的验证码为[：:\s]*([A-Za-z0-9]{4,8})/i,
                /您的验证码是[：:\s]*([A-Za-z0-9]{4,8})/i,
                /验证码为[：:\s]*([A-Za-z0-9]{4,8})/i,
                /验证码是[：:\s]*([A-Za-z0-9]{4,8})/i,

                // 其他常见的精确格式
                /your code is[：:\s]*([A-Za-z0-9]{4,8})/i,
                /the code is[：:\s]*([A-Za-z0-9]{4,8})/i,
                /verification code is[：:\s]*([A-Za-z0-9]{4,8})/i,
            ];

            // 高优先级模式 - 精确匹配（支持大小写字母和数字）
            const highPriorityPatterns = [
                // 中文验证码模式 - 精确匹配，支持各种连接词
                /验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /您的验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /动态验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /邮箱验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /短信验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,

                // 英文验证码模式 - 精确匹配
                /verification code[：:\s]*is\s*([A-Za-z0-9]{4,8})/i,
                /your verification code[：:\s]*is\s*([A-Za-z0-9]{4,8})/i,
                /code[：:\s]*is\s*([A-Za-z0-9]{4,8})/i,
                /email verification code[：:\s]*[is]?\s*([A-Za-z0-9]{4,8})/i,

                // OTP模式
                /OTP[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /one.time.password[：:\s]*([A-Za-z0-9]{4,8})/i,

                // 安全码模式
                /安全码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /security code[：:\s]*([A-Za-z0-9]{4,8})/i,
            ];

            // 中等优先级模式 - 通用匹配
            const mediumPriorityPatterns = [
                // 通用验证码模式 - 支持更多连接词和格式
                /验证码[：:\s]*[为是]?\s*([A-Za-z0-9]{4,8})/i,
                /verification code[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,
                /code[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,

                // 特殊格式的验证码模式
                /验证码为[：:\s]*([A-Za-z0-9]{4,8})/i,
                /验证码是[：:\s]*([A-Za-z0-9]{4,8})/i,
                /code is[：:\s]*([A-Za-z0-9]{4,8})/i,

                // PIN码模式
                /PIN[：:\s]*码?\s*[为是]?\s*([A-Za-z0-9]{4,8})/i,
                /pin[：:\s]*code?\s*[:]?\s*([A-Za-z0-9]{4,8})/i,

                // Token模式
                /token[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,
                /令牌[：:\s]*[为是]?\s*([A-Za-z0-9]{4,8})/i,

                // 动态码模式
                /动态码[：:\s]*[为是]?\s*([A-Za-z0-9]{4,8})/i,
                /dynamic code[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,
            ];

            // 低优先级模式 - 数字匹配
            const lowPriorityPatterns = [
                // 6位数字（最常见）
                /\b(\d{6})\b/g,
                // 4-8位数字
                /\b(\d{4,8})\b/g,
            ];

            // 按优先级顺序检查
            const allPatterns = [
                { patterns: ultraHighPriorityPatterns, priority: 'ultra-high' },
                { patterns: highPriorityPatterns, priority: 'high' },
                { patterns: mediumPriorityPatterns, priority: 'medium' },
                { patterns: lowPriorityPatterns, priority: 'low' }
            ];

            for (const group of allPatterns) {
                for (const pattern of group.patterns) {
                    const matches = textContent.match(pattern);
                    if (matches) {
                        // 对于全局匹配，取第一个匹配项
                        const code = matches[1] || matches[0];
                        if (code && isValidVerificationCode(code, group.priority)) {
                            return formatVerificationCode(code);
                        }
                    }
                }
            }

            return null;
        }

        // 验证码有效性检查
        function isValidVerificationCode(code, priority) {
            const trimmedCode = code.trim();

            // 长度检查
            if (trimmedCode.length < 4 || trimmedCode.length > 8) {
                return false;
            }

            // 超高优先级：允许所有格式，最宽松的验证
            if (priority === 'ultra-high') {
                return true;
            }

            // 高优先级：允许所有格式
            if (priority === 'high') {
                return true;
            }

            // 中等优先级：排除纯字母（除非是特定格式）
            if (priority === 'medium') {
                // 允许数字、数字+字母组合
                return /\d/.test(trimmedCode);
            }

            // 低优先级：仅数字，且排除明显不是验证码的数字
            if (priority === 'low') {
                // 排除年份、日期等
                const num = parseInt(trimmedCode);
                if (num < 1000 || num > 99999999) return false;
                if (trimmedCode.match(/^(19|20)\d{2}$/)) return false; // 年份
                if (trimmedCode.match(/^(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])$/)) return false; // 日期
                return true;
            }

            return false;
        }

        // 格式化验证码
        function formatVerificationCode(code) {
            const trimmed = code.trim();
            // 如果是纯数字，保持原样；如果包含字母，转为大写
            return /^\d+$/.test(trimmed) ? trimmed : trimmed.toUpperCase();
        }

        // 增强的验证码识别（用于单个邮件显示）
        function detectAndHighlightVerificationCode(content) {
            const code = detectVerificationCode(content);
            if (code) {
                // 在内容中高亮显示验证码，添加唯一ID和点击属性
                const uniqueId = 'code-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                const highlightedContent = content.replace(
                    new RegExp(`\\b${code}\\b`, 'gi'),
                    `<span class="highlighted-code" id="${uniqueId}" data-code="${code}" onclick="copyVerificationCode('${code}', '${uniqueId}')" title="点击复制验证码">${code}</span>`
                );
                return { code, content: highlightedContent };
            }
            return { code: null, content };
        }

        // 显示验证码提示
        function showVerificationCodeAlert(code) {
            // 移除已存在的提示
            const existingAlert = document.querySelector('.verification-code-alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            // 创建新的提示
            const alert = document.createElement('div');
            alert.className = 'verification-code-alert';
            alert.innerHTML = `
                <i class="ri-key-2-line"></i>
                检测到验证码:
                <span class="code-display">${code}</span>
            `;

            document.body.appendChild(alert);

            // 3秒后自动消失
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);

            // 点击复制验证码
            alert.addEventListener('click', () => {
                navigator.clipboard.writeText(code).then(() => {
                    showNotification(`验证码 ${code} 已复制到剪贴板`, 'success');
                    alert.remove();
                }).catch(() => {
                    showNotification('复制失败，请手动复制', 'error');
                });
            });
        }

        // 复制验证码功能
        function copyVerificationCode(code, elementId) {
            // 防止事件冒泡
            event.stopPropagation();

            // 获取目标元素
            const element = document.getElementById(elementId);

            // 复制到剪贴板
            navigator.clipboard.writeText(code).then(() => {
                // 复制成功
                showNotification(`验证码 ${code} 已复制到剪贴板`, 'success');

                // 添加成功样式
                if (element) {
                    element.classList.add('copied');

                    // 临时改变文本内容
                    const originalText = element.textContent;
                    element.textContent = '已复制!';

                    // 1秒后恢复原样
                    setTimeout(() => {
                        element.classList.remove('copied');
                        element.textContent = originalText;
                    }, 1000);
                }
            }).catch((error) => {
                // 复制失败，尝试使用备用方法
                console.error('复制失败，尝试备用方法:', error);

                // 备用复制方法
                if (fallbackCopyToClipboard(code)) {
                    showNotification(`验证码 ${code} 已复制到剪贴板`, 'success');

                    if (element) {
                        element.classList.add('copied');
                        const originalText = element.textContent;
                        element.textContent = '已复制!';

                        setTimeout(() => {
                            element.classList.remove('copied');
                            element.textContent = originalText;
                        }, 1000);
                    }
                } else {
                    showNotification('复制失败，请手动复制验证码', 'error');
                }
            });
        }

        // 备用复制方法（兼容旧浏览器）
        function fallbackCopyToClipboard(text) {
            try {
                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);

                // 选择并复制
                textArea.focus();
                textArea.select();
                const successful = document.execCommand('copy');

                // 清理
                document.body.removeChild(textArea);

                return successful;
            } catch (err) {
                console.error('备用复制方法也失败了:', err);
                return false;
            }
        }

        // 为动态添加的验证码元素绑定点击事件（事件委托）
        function setupVerificationCodeClickHandlers() {
            // 使用事件委托，监听整个邮件查看区域的点击事件
            const mailViewer = document.getElementById('mailViewer');
            if (mailViewer) {
                mailViewer.addEventListener('click', function(event) {
                    // 检查点击的是否是验证码元素
                    if (event.target.classList.contains('highlighted-code')) {
                        const code = event.target.getAttribute('data-code');
                        const elementId = event.target.id;
                        if (code && elementId) {
                            copyVerificationCode(code, elementId);
                        }
                    }
                });
            }
        }

        // 账号选择处理
        function handleAccountSelection(email, isSelected) {
            if (isSelected) {
                selectedAccounts.add(email);
                document.getElementById(`account-item-${email.replace('@', '-').replace('.', '-')}`).classList.add('selected');
            } else {
                selectedAccounts.delete(email);
                document.getElementById(`account-item-${email.replace('@', '-').replace('.', '-')}`).classList.remove('selected');
            }

            updateBatchControlPanel();
            updateSelectAllCheckbox();
        }

        // 更新批量控制面板
        function updateBatchControlPanel() {
            const panel = document.getElementById('batchControlPanel');
            const selectedCount = document.getElementById('selectedCount');

            if (selectedAccounts.size > 0) {
                panel.style.display = 'block';
                selectedCount.textContent = selectedAccounts.size;
            } else {
                panel.style.display = 'none';
            }
        }

        // 更新全选复选框状态
        function updateSelectAllCheckbox() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const currentPageCheckboxes = document.querySelectorAll('.account-checkbox');

            if (currentPageCheckboxes.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
                return;
            }

            const checkedCount = Array.from(currentPageCheckboxes).filter(cb => cb.checked).length;

            if (checkedCount === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCount === currentPageCheckboxes.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        // 全选/取消全选
        function handleSelectAll(isSelected) {
            const currentPageCheckboxes = document.querySelectorAll('.account-checkbox');

            currentPageCheckboxes.forEach(checkbox => {
                const email = checkbox.dataset.email;
                checkbox.checked = isSelected;
                handleAccountSelection(email, isSelected);
            });
        }

        // 清除选择
        function clearSelection() {
            selectedAccounts.clear();
            document.querySelectorAll('.account-checkbox').forEach(cb => cb.checked = false);
            document.querySelectorAll('.account-item').forEach(item => item.classList.remove('selected'));
            updateBatchControlPanel();
            updateSelectAllCheckbox();
        }

        // 批量处理邮件
        async function processBatchEmails(mailbox, type) {
            if (selectedAccounts.size === 0) {
                showNotification('请先选择要处理的账号', 'error');
                return;
            }

            if (!batchProcessor) {
                batchProcessor = new BatchEmailProcessor();
            }

            const accountsArray = Array.from(selectedAccounts);
            await batchProcessor.processBatch(accountsArray, mailbox, type);
        }

        // 加载账号列表
        async function loadAccounts(page = 1) {
            console.log("正在执行loadAccounts函数，页码：", page);
            // 确保更新全局当前页变量
            currentPage = page;
            
            const loadingSpinner = document.getElementById('loadingSpinner');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'block';
            } else {
                console.log("警告：未找到loadingSpinner元素");
            }
            
            try {
                console.log("正在获取账号数据...");
                const response = await fetch('/accounts');
                totalAccounts = await response.json();
                console.log("获取到账号数量：", Object.keys(totalAccounts).length);
                totalPages = Math.ceil(Object.keys(totalAccounts).length / ACCOUNTS_PER_PAGE);
                console.log("计算的总页数：", totalPages);
                
                // 更新分页信息
                const prevPageBtn = document.getElementById('prevPageBtn');
                const nextPageBtn = document.getElementById('nextPageBtn');
                const pageInfo = document.getElementById('pageInfo');
                
                if (prevPageBtn) prevPageBtn.disabled = page <= 1;
                if (nextPageBtn) nextPageBtn.disabled = page >= totalPages;
                if (pageInfo) pageInfo.textContent = `第 ${page} 页 / 共 ${totalPages || 1} 页`;
                
                console.log("分页按钮状态：", "上一页="+(page <= 1 ? "禁用" : "启用"), "下一页="+(page >= totalPages ? "禁用" : "启用"));
                
                // 获取当前页的账号
                const startIndex = (page - 1) * ACCOUNTS_PER_PAGE;
                const endIndex = startIndex + ACCOUNTS_PER_PAGE;
                const pageAccounts = Object.entries(totalAccounts).slice(startIndex, endIndex);
                console.log("当前页显示账号数量：", pageAccounts.length);
                
                const accountsDiv = document.getElementById('accounts');
                if (!accountsDiv) {
                    console.error("错误：未找到accounts元素");
                    return;
                }
                
                accountsDiv.innerHTML = '';
                
                if (pageAccounts.length === 0) {
                    accountsDiv.innerHTML = '<div class="no-accounts">暂无账号，请先添加账号</div>';
                    console.log("没有账号可显示");
                } else {
                    console.log("正在创建账号列表...");
                    pageAccounts.forEach(([email, account], index) => {
                        const accountDiv = document.createElement('div');
                        accountDiv.classList.add('account-item');
                        accountDiv.id = `account-item-${email.replace('@', '-').replace('.', '-')}`;
                        accountDiv.innerHTML = `
                            <div class="account-email-container">
                                <input type="checkbox" class="account-checkbox" data-email="${email}" onchange="handleAccountSelection('${email}', this.checked)">
                                <div class="account-email" onclick="copyToClipboard('${email}')">
                                    ${startIndex + index + 1}. ${email}
                                    <span class="mail-count" id="count-${email.replace('@', '-').replace('.', '-')}">0</span>
                                </div>
                            </div>
                            <div class="account-actions">
                                <button onclick="fetchMail('${email}', 'INBOX', 'new')" class="btn-mail">
                                    <i class="ri-mail-line"></i> 新邮件
                                </button>
                                <button onclick="fetchMail('${email}', 'Junk', 'new')" class="btn-mail">
                                    <i class="ri-spam-2-line"></i> 新垃圾邮件
                                </button>
                                <button onclick="fetchMail('${email}', 'INBOX', 'all')" class="btn-mail">
                                    <i class="ri-mail-open-line"></i> 全部邮件
                                </button>
                                <button onclick="fetchMail('${email}', 'Junk', 'all')" class="btn-mail">
                                    <i class="ri-spam-line"></i> 全部垃圾邮件
                                </button>
                            </div>
                        `;
                        accountsDiv.appendChild(accountDiv);

                        // 恢复选择状态
                        if (selectedAccounts.has(email)) {
                            const checkbox = accountDiv.querySelector('.account-checkbox');
                            checkbox.checked = true;
                            accountDiv.classList.add('selected');
                        }

                        // 获取该账号的邮件操作计数
                        fetch(`/api/mail/count/${email}`)
                            .then(response => response.json())
                            .then(data => {
                                const countElement = document.getElementById(`count-${email.replace('@', '-').replace('.', '-')}`);
                                if (countElement) {
                                    countElement.textContent = data.count;
                                }
                            })
                            .catch(error => console.error('获取邮件计数失败:', error));
                    });

                    // 更新批量控制面板和全选状态
                    updateBatchControlPanel();
                    updateSelectAllCheckbox();
                }
            } catch (error) {
                console.error('加载账号失败:', error);
                const accountsDiv = document.getElementById('accounts');
                if (accountsDiv) {
                    accountsDiv.innerHTML = '<div class="error-message">加载账号失败，请刷新页面重试</div>';
                }
            } finally {
                const loadingSpinner = document.getElementById('loadingSpinner');
                if (loadingSpinner) {
                    loadingSpinner.style.display = 'none';
                }
            }
        }
        
        // 页面切换
        function changePage(delta) {
            console.log("正在执行changePage函数，增量：", delta);
            console.log("当前页码：", currentPage, "总页数：", totalPages);
            const newPage = currentPage + delta;
            console.log("计算的新页码：", newPage);
            if (newPage >= 1 && newPage <= totalPages) {
                console.log("页码在有效范围内，切换到页码：", newPage);
                currentPage = newPage;
                loadAccounts(currentPage);
                
                // 重置邮件查看区域
                showMailPlaceholder();
                currentActiveAccount = null;

                // 重置选择状态
                updateSelectAllCheckbox();
                updateBatchControlPanel();
            } else {
                console.log("页码超出范围，不执行切换");
            }
        }
        
        // 构建API URL
        function buildApiUrl(email, mailbox, type) {
            const account = totalAccounts[email];
            if (!account) {
                showNotification('账号不存在！', 'error');
                return null;
            }
            
            if (type === 'new') {
                return `${API_BASE_URL}/mail-new?refresh_token=${account.refresh_token}&client_id=${account.client_id}&email=${email}&mailbox=${mailbox}&response_type=html`;
            } else if (type === 'all') {
                return `${API_BASE_URL}/mail-all?refresh_token=${account.refresh_token}&client_id=${account.client_id}&email=${email}&mailbox=${mailbox}&response_type=html`;
            }
            
            return null;
        }
        
        // 高亮活动账号
        function highlightActiveAccount(email) {
            // 移除所有之前的高亮
            document.querySelectorAll('.account-item').forEach(item => {
                item.classList.remove('active-account');
            });
            
            // 高亮当前账号
            const accountItem = document.getElementById(`account-item-${email.replace('@', '-').replace('.', '-')}`);
            if (accountItem) {
                accountItem.classList.add('active-account');
                // 确保高亮的元素可见（如果需要滚动）
                accountItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
            
            currentActiveAccount = email;
        }
        
        // 显示邮件占位符
        function showMailPlaceholder() {
            const mailPlaceholder = document.getElementById('mailPlaceholder');
            const mailViewer = document.getElementById('mailViewer');
            
            if (mailPlaceholder) mailPlaceholder.style.display = 'flex';
            if (mailViewer) mailViewer.style.display = 'none';
            
            // 确保加载中遮罩被隐藏
            const mailLoadingOverlay = document.getElementById('mailLoadingOverlay');
            if (mailLoadingOverlay) mailLoadingOverlay.style.display = 'none';
        }
        
        // 显示邮件内容
        function showMailContent(html) {
            const mailPlaceholder = document.getElementById('mailPlaceholder');
            const mailViewer = document.getElementById('mailViewer');

            if (mailPlaceholder) mailPlaceholder.style.display = 'none';
            if (mailViewer) {
                mailViewer.style.display = 'block';

                // 尝试解析返回内容，处理可能的JSON格式
                try {
                    // 检查是否为JSON字符串
                    if (html.trim().startsWith('[') || html.trim().startsWith('{')) {
                        const jsonData = JSON.parse(html);

                        // 处理JSON格式的邮件内容
                        if (Array.isArray(jsonData)) {
                            // 如果是数组，可能是mail-all返回的格式
                            let formattedHtml = '<div class="email-list">';

                            jsonData.forEach((item, index) => {
                                const sender = item.send || '未知发件人';
                                const subject = item.subject || '无主题';
                                const content = item.html || '';

                                // 识别验证码并高亮显示
                                const { code, content: highlightedContent } = detectAndHighlightVerificationCode(content);

                                formattedHtml += `
                                    <div class="email-item">
                                        <div class="email-header">
                                            <h3>邮件 ${index + 1}</h3>
                                            <p><strong>发件人:</strong> ${sender}</p>
                                            <p><strong>主题:</strong> ${subject}</p>
                                            <div class="verification-code-status">
                                                <strong>验证码:</strong>
                                                <span class="code-value">${code || '无验证码'}</span>
                                            </div>
                                        </div>
                                        <div class="email-content">
                                            ${highlightedContent}
                                        </div>
                                        <hr>
                                    </div>
                                `;
                            });

                            formattedHtml += '</div>';
                            mailViewer.innerHTML = formattedHtml;
                        } else {
                            // 单个邮件对象
                            const sender = jsonData.send || '未知发件人';
                            const subject = jsonData.subject || '无主题';
                            const content = jsonData.html || '';

                            // 识别验证码并高亮显示
                            const { code, content: highlightedContent } = detectAndHighlightVerificationCode(content);

                            const formattedHtml = `
                                <div class="email-item">
                                    <div class="email-header">
                                        <p><strong>发件人:</strong> ${sender}</p>
                                        <p><strong>主题:</strong> ${subject}</p>
                                        <div class="verification-code-status">
                                            <strong>验证码:</strong>
                                            <span class="code-value">${code || '无验证码'}</span>
                                        </div>
                                    </div>
                                    <div class="email-content">
                                        ${highlightedContent}
                                    </div>
                                </div>
                            `;

                            mailViewer.innerHTML = formattedHtml;

                            // 如果检测到验证码，显示提示
                            if (code) {
                                showVerificationCodeAlert(code);
                            }
                        }
                    } else {
                        // 不是JSON，直接显示HTML内容，但仍然进行验证码识别
                        const { code, content: highlightedContent } = detectAndHighlightVerificationCode(html);

                        // 在内容顶部添加验证码状态
                        const contentWithCodeStatus = `
                            <div class="verification-code-banner">
                                <strong>验证码识别结果:</strong>
                                <span class="code-value">${code || '无验证码'}</span>
                            </div>
                            ${highlightedContent}
                        `;

                        mailViewer.innerHTML = contentWithCodeStatus;

                        // 如果检测到验证码，显示提示
                        if (code) {
                            showVerificationCodeAlert(code);
                        }
                    }
                } catch (error) {
                    // 解析失败，直接显示原始内容，但仍然进行验证码识别
                    console.error('解析邮件内容失败:', error);
                    const { code, content: highlightedContent } = detectAndHighlightVerificationCode(html);

                    const contentWithCodeStatus = `
                        <div class="verification-code-banner">
                            <strong>验证码识别结果:</strong>
                            <span class="code-value">${code || '无验证码'}</span>
                        </div>
                        ${highlightedContent}
                    `;

                    mailViewer.innerHTML = contentWithCodeStatus;

                    if (code) {
                        showVerificationCodeAlert(code);
                    }
                }
            }

            // 确保加载中遮罩被隐藏
            const mailLoadingOverlay = document.getElementById('mailLoadingOverlay');
            if (mailLoadingOverlay) mailLoadingOverlay.style.display = 'none';
        }
        
        // 获取邮件并增加计数
        async function fetchMail(email, mailbox, type) {
            // 高亮当前选中的账号
            highlightActiveAccount(email);
            
            // 显示加载中遮罩
            const mailLoadingOverlay = document.getElementById('mailLoadingOverlay');
            if (mailLoadingOverlay) mailLoadingOverlay.style.display = 'flex';
            
            try {
                // 先增加计数
                const countResponse = await fetch(`/api/mail/count/${email}`, {
                    method: 'POST'
                });
                const countData = await countResponse.json();
                
                if (countData.success) {
                    // 更新显示的计数
                    const countElement = document.getElementById(`count-${email.replace('@', '-').replace('.', '-')}`);
                    if (countElement) {
                        countElement.textContent = countData.count;
                    }
                }
                
                // 构建API URL
                const url = buildApiUrl(email, mailbox, type);
                if (!url) {
                    throw new Error('无法构建API URL');
                }
                
                // 获取邮件内容
                const mailResponse = await fetch(url);
                
                if (!mailResponse.ok) {
                    throw new Error(`请求失败: ${mailResponse.status}`);
                }
                
                // 对所有类型的邮件请求都使用相同的方式处理响应
                const mailHtml = await mailResponse.text();
                
                // 显示邮件内容
                showMailContent(mailHtml);
                
                // 显示成功通知
                let actionType = '';
                if (type === 'new') {
                    actionType = mailbox === 'INBOX' ? '新邮件' : '新垃圾邮件';
                } else {
                    actionType = mailbox === 'INBOX' ? '全部邮件' : '全部垃圾邮件';
                }
                showNotification(`已加载 ${email} 的${actionType}`, 'success');
                
                // 邮件加载完成后，自动滚动到页面顶部
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
                
            } catch (error) {
                console.error('邮件获取失败:', error);
                showNotification('邮件获取失败，请重试', 'error');
                showMailPlaceholder();
                
                // 隐藏加载中遮罩
                if (mailLoadingOverlay) {
                    mailLoadingOverlay.style.display = 'none';
                }
            }
        }
        
        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('邮箱地址已复制到剪贴板', 'success');
            }, () => {
                showNotification('复制失败，请手动复制', 'error');
            });
        }
        
        // 显示通知
        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            if (!notification) return;
            
            notification.textContent = message;
            notification.className = 'notification ' + type + ' show';
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html> 