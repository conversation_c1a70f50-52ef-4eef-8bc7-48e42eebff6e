<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码识别测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-input {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #4caf50;
        }
        .highlighted-code {
            background-color: #ffeb3b;
            color: #333;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            border: 2px solid #ffc107;
        }
        button {
            background: #0078d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
    </style>
</head>
<body>
    <h1>验证码识别功能测试</h1>
    
    <div class="test-container">
        <h3>测试邮件内容</h3>
        <textarea class="test-input" id="testInput" placeholder="在此输入邮件内容进行测试...">
您的验证码是：123456，请在5分钟内使用。
        </textarea>
        <button onclick="testVerificationCode()">测试验证码识别</button>
        <button onclick="loadTestCases()">加载测试用例</button>
        
        <div id="testResult" class="test-result" style="display:none;">
            <h4>识别结果：</h4>
            <p><strong>验证码：</strong><span id="detectedCode"></span></p>
            <p><strong>高亮内容：</strong></p>
            <div id="highlightedContent"></div>
        </div>
    </div>
    
    <div class="test-container">
        <h3>预设测试用例</h3>
        <div id="testCases"></div>
    </div>

    <script>
        // 复制验证码识别函数
        function detectVerificationCode(content) {
            let textContent = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ');
            
            const highPriorityPatterns = [
                /验证码[：:\s]*[是为]?\s*([A-Z0-9]{4,8})/i,
                /您的验证码[：:\s]*([A-Z0-9]{4,8})/i,
                /动态验证码[：:\s]*([A-Z0-9]{4,8})/i,
                /verification code[：:\s]*is\s*([A-Z0-9]{4,8})/i,
                /your verification code[：:\s]*is\s*([A-Z0-9]{4,8})/i,
                /code[：:\s]*is\s*([A-Z0-9]{4,8})/i,
                /OTP[：:\s]*[是为]?\s*([A-Z0-9]{4,8})/i,
                /one.time.password[：:\s]*([A-Z0-9]{4,8})/i,
                /安全码[：:\s]*([A-Z0-9]{4,8})/i,
                /security code[：:\s]*([A-Z0-9]{4,8})/i,
            ];
            
            const mediumPriorityPatterns = [
                /验证码[：:\s]*([A-Za-z0-9]{4,8})/i,
                /verification code[：:\s]*([A-Za-z0-9]{4,8})/i,
                /code[：:\s]*([A-Za-z0-9]{4,8})/i,
                /PIN[：:\s]*码?\s*([A-Za-z0-9]{4,8})/i,
                /pin[：:\s]*code?\s*([A-Za-z0-9]{4,8})/i,
                /token[：:\s]*([A-Za-z0-9]{4,8})/i,
                /令牌[：:\s]*([A-Za-z0-9]{4,8})/i,
            ];
            
            const lowPriorityPatterns = [
                /\b(\d{6})\b/g,
                /\b(\d{4,8})\b/g,
            ];
            
            const allPatterns = [
                { patterns: highPriorityPatterns, priority: 'high' },
                { patterns: mediumPriorityPatterns, priority: 'medium' },
                { patterns: lowPriorityPatterns, priority: 'low' }
            ];
            
            for (const group of allPatterns) {
                for (const pattern of group.patterns) {
                    const matches = textContent.match(pattern);
                    if (matches) {
                        const code = matches[1] || matches[0];
                        if (code && isValidVerificationCode(code, group.priority)) {
                            return formatVerificationCode(code);
                        }
                    }
                }
            }
            
            return null;
        }
        
        function isValidVerificationCode(code, priority) {
            const trimmedCode = code.trim();
            
            if (trimmedCode.length < 4 || trimmedCode.length > 8) {
                return false;
            }
            
            if (priority === 'high') {
                return true;
            }
            
            if (priority === 'medium') {
                return /\d/.test(trimmedCode);
            }
            
            if (priority === 'low') {
                const num = parseInt(trimmedCode);
                if (num < 1000 || num > 99999999) return false;
                if (trimmedCode.match(/^(19|20)\d{2}$/)) return false;
                if (trimmedCode.match(/^(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])$/)) return false;
                return true;
            }
            
            return false;
        }
        
        function formatVerificationCode(code) {
            const trimmed = code.trim();
            return /^\d+$/.test(trimmed) ? trimmed : trimmed.toUpperCase();
        }
        
        function detectAndHighlightVerificationCode(content) {
            const code = detectVerificationCode(content);
            if (code) {
                const highlightedContent = content.replace(
                    new RegExp(`\\b${code}\\b`, 'gi'),
                    `<span class="highlighted-code">${code}</span>`
                );
                return { code, content: highlightedContent };
            }
            return { code: null, content };
        }
        
        function testVerificationCode() {
            const input = document.getElementById('testInput').value;
            const result = detectAndHighlightVerificationCode(input);
            
            document.getElementById('detectedCode').textContent = result.code || '无验证码';
            document.getElementById('highlightedContent').innerHTML = result.content;
            document.getElementById('testResult').style.display = 'block';
        }
        
        function loadTestCases() {
            const testCases = [
                "您的验证码是：123456，请在5分钟内使用。",
                "Your verification code is: ABC123",
                "验证码为 789012，有效期10分钟",
                "OTP: 456789",
                "安全码：XY7890",
                "PIN码：5678",
                "这是一封没有验证码的邮件",
                "今天是2024年1月1日，没有验证码",
                "verification code is 987654",
                "动态验证码 QW3456"
            ];
            
            const container = document.getElementById('testCases');
            container.innerHTML = '';
            
            testCases.forEach((testCase, index) => {
                const result = detectAndHighlightVerificationCode(testCase);
                const div = document.createElement('div');
                div.style.cssText = 'margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;';
                div.innerHTML = `
                    <p><strong>测试用例 ${index + 1}:</strong></p>
                    <p>${result.content}</p>
                    <p><strong>识别结果:</strong> ${result.code || '无验证码'}</p>
                `;
                container.appendChild(div);
            });
        }
        
        // 页面加载时自动加载测试用例
        window.onload = function() {
            loadTestCases();
        };
    </script>
</body>
</html>
