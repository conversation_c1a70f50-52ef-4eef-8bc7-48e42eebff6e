<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码识别修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-case {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        .test-result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #4caf50;
        }
        .test-result.fail {
            background: #ffeaea;
            border-left-color: #f44336;
        }
        .highlighted-code {
            background-color: #ffeb3b;
            color: #333;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            border: 2px solid #ffc107;
        }
        button {
            background: #0078d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .email-content {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
        }
        .priority-label {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .ultra-high { background: #ff4444; color: white; }
        .high { background: #ff9800; color: white; }
        .medium { background: #2196f3; color: white; }
        .low { background: #9e9e9e; color: white; }
    </style>
</head>
<body>
    <h1>验证码识别修复测试</h1>
    
    <div class="test-container">
        <h3>问题案例测试</h3>
        <p><strong>原始问题：</strong>邮件内容包含验证码 <code>e9b1e1</code>，但系统识别为"无验证码"</p>
        
        <div class="test-case">
            <h4>测试用例 1：原始问题案例</h4>
            <div class="email-content">您的验证码为: e9b1e1</div>
            <button onclick="testCase('您的验证码为: e9b1e1', 1)">测试识别</button>
            <div id="result1"></div>
        </div>
        
        <div class="test-case">
            <h4>测试用例 2：类似格式变体</h4>
            <div class="email-content">您的验证码是: a1b2c3</div>
            <button onclick="testCase('您的验证码是: a1b2c3', 2)">测试识别</button>
            <div id="result2"></div>
        </div>
        
        <div class="test-case">
            <h4>测试用例 3：验证码为格式</h4>
            <div class="email-content">验证码为: x9y8z7</div>
            <button onclick="testCase('验证码为: x9y8z7', 3)">测试识别</button>
            <div id="result3"></div>
        </div>
        
        <div class="test-case">
            <h4>测试用例 4：验证码是格式</h4>
            <div class="email-content">验证码是: 123abc</div>
            <button onclick="testCase('验证码是: 123abc', 4)">测试识别</button>
            <div id="result4"></div>
        </div>
    </div>
    
    <div class="test-container">
        <h3>回归测试 - 确保原有功能正常</h3>
        
        <div class="test-case">
            <h4>测试用例 5：传统数字验证码</h4>
            <div class="email-content">您的验证码：123456</div>
            <button onclick="testCase('您的验证码：123456', 5)">测试识别</button>
            <div id="result5"></div>
        </div>
        
        <div class="test-case">
            <h4>测试用例 6：英文验证码</h4>
            <div class="email-content">Your verification code is: ABC123</div>
            <button onclick="testCase('Your verification code is: ABC123', 6)">测试识别</button>
            <div id="result6"></div>
        </div>
        
        <div class="test-case">
            <h4>测试用例 7：OTP格式</h4>
            <div class="email-content">OTP: 789012</div>
            <button onclick="testCase('OTP: 789012', 7)">测试识别</button>
            <div id="result7"></div>
        </div>
    </div>
    
    <div class="test-container">
        <h3>边界测试</h3>
        
        <div class="test-case">
            <h4>测试用例 8：无验证码内容</h4>
            <div class="email-content">这是一封普通邮件，没有验证码。</div>
            <button onclick="testCase('这是一封普通邮件，没有验证码。', 8)">测试识别</button>
            <div id="result8"></div>
        </div>
        
        <div class="test-case">
            <h4>测试用例 9：包含年份的内容（应该被排除）</h4>
            <div class="email-content">今年是2024年，祝您新年快乐！</div>
            <button onclick="testCase('今年是2024年，祝您新年快乐！', 9)">测试识别</button>
            <div id="result9"></div>
        </div>
    </div>
    
    <button onclick="runAllTests()">运行所有测试</button>

    <script>
        // 复制修复后的验证码识别函数
        function detectVerificationCode(content) {
            let textContent = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ');
            
            // 超高优先级模式 - 针对特定格式的精确匹配
            const ultraHighPriorityPatterns = [
                /您的验证码为[：:\s]*([A-Za-z0-9]{4,8})/i,
                /您的验证码是[：:\s]*([A-Za-z0-9]{4,8})/i,
                /验证码为[：:\s]*([A-Za-z0-9]{4,8})/i,
                /验证码是[：:\s]*([A-Za-z0-9]{4,8})/i,
                /your code is[：:\s]*([A-Za-z0-9]{4,8})/i,
                /the code is[：:\s]*([A-Za-z0-9]{4,8})/i,
                /verification code is[：:\s]*([A-Za-z0-9]{4,8})/i,
            ];
            
            // 高优先级模式
            const highPriorityPatterns = [
                /验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /您的验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /动态验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /邮箱验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /短信验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /verification code[：:\s]*is\s*([A-Za-z0-9]{4,8})/i,
                /your verification code[：:\s]*is\s*([A-Za-z0-9]{4,8})/i,
                /code[：:\s]*is\s*([A-Za-z0-9]{4,8})/i,
                /email verification code[：:\s]*[is]?\s*([A-Za-z0-9]{4,8})/i,
                /OTP[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /one.time.password[：:\s]*([A-Za-z0-9]{4,8})/i,
                /安全码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
                /security code[：:\s]*([A-Za-z0-9]{4,8})/i,
            ];
            
            // 中等优先级模式
            const mediumPriorityPatterns = [
                /验证码[：:\s]*[为是]?\s*([A-Za-z0-9]{4,8})/i,
                /verification code[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,
                /code[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,
                /验证码为[：:\s]*([A-Za-z0-9]{4,8})/i,
                /验证码是[：:\s]*([A-Za-z0-9]{4,8})/i,
                /code is[：:\s]*([A-Za-z0-9]{4,8})/i,
                /PIN[：:\s]*码?\s*[为是]?\s*([A-Za-z0-9]{4,8})/i,
                /pin[：:\s]*code?\s*[:]?\s*([A-Za-z0-9]{4,8})/i,
                /token[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,
                /令牌[：:\s]*[为是]?\s*([A-Za-z0-9]{4,8})/i,
                /动态码[：:\s]*[为是]?\s*([A-Za-z0-9]{4,8})/i,
                /dynamic code[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,
            ];
            
            // 低优先级模式
            const lowPriorityPatterns = [
                /\b(\d{6})\b/g,
                /\b(\d{4,8})\b/g,
            ];
            
            const allPatterns = [
                { patterns: ultraHighPriorityPatterns, priority: 'ultra-high' },
                { patterns: highPriorityPatterns, priority: 'high' },
                { patterns: mediumPriorityPatterns, priority: 'medium' },
                { patterns: lowPriorityPatterns, priority: 'low' }
            ];
            
            for (const group of allPatterns) {
                for (const pattern of group.patterns) {
                    const matches = textContent.match(pattern);
                    if (matches) {
                        const code = matches[1] || matches[0];
                        if (code && isValidVerificationCode(code, group.priority)) {
                            return { code: formatVerificationCode(code), priority: group.priority };
                        }
                    }
                }
            }
            
            return { code: null, priority: null };
        }
        
        function isValidVerificationCode(code, priority) {
            const trimmedCode = code.trim();
            
            if (trimmedCode.length < 4 || trimmedCode.length > 8) {
                return false;
            }
            
            if (priority === 'ultra-high' || priority === 'high') {
                return true;
            }
            
            if (priority === 'medium') {
                return /\d/.test(trimmedCode);
            }
            
            if (priority === 'low') {
                const num = parseInt(trimmedCode);
                if (num < 1000 || num > 99999999) return false;
                if (trimmedCode.match(/^(19|20)\d{2}$/)) return false;
                if (trimmedCode.match(/^(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])$/)) return false;
                return true;
            }
            
            return false;
        }
        
        function formatVerificationCode(code) {
            const trimmed = code.trim();
            return /^\d+$/.test(trimmed) ? trimmed : trimmed.toUpperCase();
        }
        
        function testCase(content, caseNumber) {
            const result = detectVerificationCode(content);
            const resultDiv = document.getElementById(`result${caseNumber}`);
            
            const priorityClass = result.priority ? result.priority.replace('-', '') : '';
            const priorityLabel = result.priority ? `<span class="priority-label ${priorityClass}">${result.priority}</span>` : '';
            
            if (result.code) {
                resultDiv.innerHTML = `
                    <div class="test-result">
                        <strong>✅ 识别成功:</strong> <span class="highlighted-code">${result.code}</span>
                        ${priorityLabel}
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result fail">
                        <strong>❌ 识别失败:</strong> 无验证码
                    </div>
                `;
            }
        }
        
        function runAllTests() {
            const testCases = [
                '您的验证码为: e9b1e1',
                '您的验证码是: a1b2c3',
                '验证码为: x9y8z7',
                '验证码是: 123abc',
                '您的验证码：123456',
                'Your verification code is: ABC123',
                'OTP: 789012',
                '这是一封普通邮件，没有验证码。',
                '今年是2024年，祝您新年快乐！'
            ];
            
            testCases.forEach((testCase, index) => {
                testCase(testCase, index + 1);
            });
        }
        
        // 页面加载时自动运行所有测试
        window.onload = function() {
            runAllTests();
        };
    </script>
</body>
</html>
